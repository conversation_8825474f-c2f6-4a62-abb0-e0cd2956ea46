import { Controller, Get, UseGuards, Query } from '@nestjs/common';
import { AdminsService } from './admins.service';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '@nestjs/passport';

@ApiTags('Admins')
@ApiBearerAuth()
@UseGuards(AuthGuard('jwt'))
@Controller({
  path: 'admins',
  version: '1',
})
export class AdminsController {
  constructor(private readonly adminsService: AdminsService) {}

  @Get('analytics/stats')
  async getAnalyticsStats(
    @Query('period') period: '7days' | '30days' | '90days' = '7days',
  ) {
    return this.adminsService.getDashboardSummary(period);
  }

  @Get('activity/stats')
  async getActivityStatistics(
    @Query('period') period?: '7days' | '30days' | '90days',
  ) {
    return this.adminsService.getActivityStatistics(period || '7days');
  }

  @Get('activity/list')
  async getOfficeActivityList(
    @Query('period') period?: '7days' | '30days' | '90days',
    @Query('cityId') cityId?: string,
    @Query('countryId') countryId?: string,
    @Query('isActiveOnly') isActiveOnly?: boolean,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    return this.adminsService.getOfficeActivityList({
      period: period || '7days',
      cityId,
      countryId,
      isActiveOnly,
      limit,
      offset,
    });
  }

  @Get('dashboard/stats')
  async getDashboardStats(
    @Query('period') period: '7days' | '30days' | '90days' = '7days',
  ) {
    return this.adminsService.getDashboardStats(period);
  }

  @Get('dashboard/table')
  async getDashboardTable(
    @Query('period') period?: '7days' | '30days' | '90days',
    @Query('cityId') cityId?: string,
    @Query('countryId') countryId?: string,
    @Query('isActiveOnly') isActiveOnly?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
  ) {
    // Convert page-based pagination to offset-based for service layer
    const pageNumber = page && page > 0 ? page : 1;
    const pageSize = limit && limit > 0 ? limit : 10;
    const offset = (pageNumber - 1) * pageSize;

    const result = await this.adminsService.getDashboardTable({
      period: period || '7days',
      cityId,
      countryId,
      isActiveOnly,
      limit: pageSize,
      offset,
      search,
    });

    // Add pagination metadata to response
    const totalPages = Math.ceil(result.totalOffices / pageSize);

    return {
      ...result,
      pagination: {
        currentPage: pageNumber,
        pageSize,
        totalPages,
        totalItems: result.totalOffices,
        hasNextPage: pageNumber < totalPages,
        hasPreviousPage: pageNumber > 1,
      },
    };
  }

  @Get('office-engagement')
  async getOfficeEngagement(
    @Query('period') period?: '7days' | '30days' | '90days',
    @Query('cityId') cityId?: string,
    @Query('countryId') countryId?: string,
    @Query('isActiveOnly') isActiveOnly?: boolean,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
  ) {
    // Convert page-based pagination to offset-based for service layer
    const pageNumber = page && page > 0 ? page : 1;
    const pageSize = limit && limit > 0 ? limit : 10;
    const offset = (pageNumber - 1) * pageSize;

    const result = await this.adminsService.getOfficeEngagement({
      period: period || '7days',
      cityId,
      countryId,
      isActiveOnly,
      limit: pageSize,
      offset,
      search,
    });

    // Add pagination metadata to response
    const totalPages = Math.ceil(result.totalOffices / pageSize);

    return {
      ...result,
      pagination: {
        currentPage: pageNumber,
        pageSize,
        totalPages,
        totalItems: result.totalOffices,
        hasNextPage: pageNumber < totalPages,
        hasPreviousPage: pageNumber > 1,
      },
    };
  }
}
